{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": [":root {\n\t/* --- colors --- */\n\t--background: #f7f7f7;\n\t--foreground: #000000;\n\t--shadow: rgba(0, 0, 0, 0.05);\n\n\t/* input */\n\t--input-surface: rgba(0, 0, 0, 0.06);\n\t--input-surface-focus: rgba(0, 0, 0, 0.08);\n\n\t/* button */\n\t--button-surface: #2e2f32;\n\t--button-surface-hover: #19191a;\n\n\t--surface: #ffffff;\n\n\t/* --- fonts --- */\n\t/* title */\n\t--font-title-size: 1.75rem;\n\t--font-title-weight: 700;\n\t--font-title-line-height: 100%;\n\t--font-title-letter-spacing: 0;\n\n\t/* subtitle */\n\t--font-subtitle-size: 1.25rem;\n\t--font-subtitle-weight: 600;\n\t--font-subtitle-line-height: 1.5rem;\n\t--font-subtitle-letter-spacing: -0.0125rem;\n\n\t/* body */\n\t--font-body-size: 1.0625rem;\n\t--font-body-weight: 400;\n\t--font-body-line-height: 1.375rem;\n\t--font-body-letter-spacing: -0.0075rem;\n\n\t/* caption */\n\t--font-caption-size: 0.625rem;\n\t--font-caption-weight: 400;\n\t--font-caption-line-height: 0.875rem;\n\t--font-caption-letter-spacing: 0.0075rem;\n}\n\nhtml,\nbody {\n\tmax-width: 100vw;\n\toverflow-x: hidden;\n}\n\nhtml {\n\tfont-size: 16px;\n}\n\nbody {\n\tcolor: var(--foreground);\n\tbackground: var(--background);\n\tfont-family: 'Inter', Helvetica, sans-serif;\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n}\n\n* {\n\tbox-sizing: border-box;\n\tpadding: 0;\n\tmargin: 0;\n}\n\na {\n\tcolor: inherit;\n\ttext-decoration: none;\n}\n\ninput {\n\tfont-family: inherit;\n\tfont-size: inherit;\n\tborder: none;\n\toutline: none;\n\tbackground: none;\n}\n\nbutton {\n\tcursor: pointer;\n\tborder: none;\n\toutline: none;\n\tbackground: none;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA"}}]}