/* [project]/app/globals.css [app-client] (css) */
:root {
  --background: #f7f7f7;
  --foreground: #000;
  --shadow: rgba(0, 0, 0, .05);
  --input-surface: rgba(0, 0, 0, .06);
  --input-surface-focus: rgba(0, 0, 0, .08);
  --button-surface: #2e2f32;
  --button-surface-hover: #19191a;
  --surface: #fff;
  --font-title-size: 1.75rem;
  --font-title-weight: 700;
  --font-title-line-height: 100%;
  --font-title-letter-spacing: 0;
  --font-subtitle-size: 1.25rem;
  --font-subtitle-weight: 600;
  --font-subtitle-line-height: 1.5rem;
  --font-subtitle-letter-spacing: -.0125rem;
  --font-body-size: 1.0625rem;
  --font-body-weight: 400;
  --font-body-line-height: 1.375rem;
  --font-body-letter-spacing: -.0075rem;
  --font-caption-size: .625rem;
  --font-caption-weight: 400;
  --font-caption-line-height: .875rem;
  --font-caption-letter-spacing: .0075rem;
}

html, body {
  max-width: 100vw;
  overflow-x: hidden;
}

html {
  font-size: 16px;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Inter, Helvetica, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

input {
  font-family: inherit;
  font-size: inherit;
  background: none;
  border: none;
  outline: none;
}

button {
  cursor: pointer;
  background: none;
  border: none;
  outline: none;
}

/*# sourceMappingURL=app_globals_css_e59ae46c._.single.css.map*/